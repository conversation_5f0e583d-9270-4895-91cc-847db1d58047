package com.wormhole.hotelds.excel.ticket;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.excel.BaseExportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author：mzy
 * @Date：2025/09/02
 * @Description：集团工单明细导出请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GroupTicketsDetailExportReq extends BaseExportReq {

    private Integer current;

    private Integer pageSize;

    private String startTime;

    private String endTime;

    private String hotelCode;

    private String ticketCategory;

    private String positionName;

    private String rtcRoomId;

    private Integer closedLoopLevel;

    private Integer handleMethod;

    private String showFlag;

    private Integer status;

    private Integer expiredFlag;

    private String ticketNo;

    private String firstCreateTime;

    private String lastCreateTime;
}
