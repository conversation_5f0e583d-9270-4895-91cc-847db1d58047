package com.wormhole.hotelds.excel.ticket;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * @Author：mzy
 * @Date：2025/09/02
 * @Description：集团工单明细导出DTO
 */
@Data
@Accessors(chain = true)
public class GroupTicketsDetailExportDTO {

    /**
     * 门店编码
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 类型
     */
    private String type;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 客人请求
     */
    private String guestRequest;

    /**
     * 对话类型
     */
    private String conversationType;

    /**
     * 处理方式
     */
    private String handleMethod;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 处理时间
     */
    private String handleTime;

    /**
     * 处理时长
     */
    private String handleDuration;

    /**
     * 聊天记录
     */
    private String chatRecord;
}
