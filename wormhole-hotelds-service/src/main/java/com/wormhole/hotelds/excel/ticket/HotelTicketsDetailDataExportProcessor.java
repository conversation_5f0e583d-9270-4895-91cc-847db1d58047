package com.wormhole.hotelds.excel.ticket;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.dao.HdsCallLogDao;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.req.GetCallLogQO;
import com.wormhole.hotelds.admin.repository.HotelRepository;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketLogResp;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExcelFormattingProvider;
import com.wormhole.hotelds.excel.ExportContext;
import com.wormhole.hotelds.util.CustomDateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.admin.constant.ExcelConstant.*;

/**
 * @Author：mzy
 * @Date：2025/09/02
 * @Description：门店工单明细导出处理器
 */
@Slf4j
@Component
public class HotelTicketsDetailDataExportProcessor implements DataExportProcessor<HotelTicketsDetailExportDTO, HotelTicketsDetailExportReq>, ExcelFormattingProvider {

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private HdsCallLogDao hdsCallLogDao;


    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.HOTEL_TICKETS_DETAIL.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return new ArrayList<>(Arrays.asList(
                FIELD_TICKET_NO,
                FIELD_TICKET_TYPE,
                FIELD_TICKET_POSITION_NAME,
                FIELD_TICKET_GUEST_REQUEST,
                FIELD_TICKET_CONVERSATION_TYPE,
                FIELD_TICKET_HANDLE_METHOD,
                FIELD_TICKET_STATUS,
                FIELD_TICKET_CREATOR,
                FIELD_TICKET_HANDLER,
                FIELD_TICKET_CREATE_TIME,
                FIELD_TICKET_HANDLE_TIME,
                FIELD_TICKET_HANDLE_DURATION,
                FIELD_TICKET_CHAT_RECORD
        ));
    }

    @Override
    public Flux<HotelTicketsDetailExportDTO> queryData(HotelTicketsDetailExportReq request, ExportContext context) {

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> hotelRepository.findByHotelCode(headerInfo.getHotelCode()))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Hotel not found" )))
                .flatMapMany(hotel -> {
                    request.setHotelName(hotel.getHotelName());

                    // 设置分页参数为导出模式
                    request.setCurrent(1);
                    request.setPageSize(2000);
                    TicketAdminPageReq ticketReq = new TicketAdminPageReq();
                    BeanUtil.copyProperties(request, ticketReq);

                    // 调用HotelDsApiClient获取工单数据
                    return hotelDsApiClient.getTicketPageFeign(ticketReq)
                            .flatMapMany(ticketFeignResult -> {
                                List<TicketAdminListResp> dataList = ticketFeignResult.getDataList();
                                if (CollUtil.isNotEmpty(dataList)) {
                                    request.setFirstCreateTime(dataList.get(0).getCreatedAt());
                                    request.setLastCreateTime(dataList.get(dataList.size() - 1).getCreatedAt());
                                    return Flux.fromIterable(dataList);
                                }
                                return Flux.empty();
                            })
                            .flatMap(this::convertToExportDTO)
                            .onErrorResume(e -> {
                                log.error("查询门店工单数据失败", e);
                                return Flux.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询工单数据失败: " + e.getMessage()));
                            });

                });
    }




    private Mono<HotelTicketsDetailExportDTO> convertToExportDTO(TicketAdminListResp ticket) {

        return hdsCallLogDao.findList(GetCallLogQO.builder().conversationId(ticket.getConversationId()).rtcRoomId(ticket.getRtcRoomId()).rtcRoomIdIsNull(StringUtils.isBlank(ticket.getRtcRoomId())).hotelCode(ticket.getHotelCode()).build(), null)
                .doOnNext(list -> log.info("查询到通话记录 {} 条", list.size()))
                .flatMap(list -> {
                    List<TicketLogResp.TicketConservationRecordResp> records = list.stream().map(item->{
                        TicketLogResp.TicketConservationRecordResp record = new TicketLogResp.TicketConservationRecordResp();
                        if(ObjectUtil.equal(item.getCallType(), 1)){
                            // 点对点
                            DeviceTypeEnum byPrefix = DeviceTypeEnum.getByPrefix(item.getCreatedBy());
                            record.setName(byPrefix == null ? "-" : (DeviceTypeEnum.judgeDeviceFromHotel(byPrefix.getCode()) ? "前台":"客人"));
                        } else {
                            record.setName(ObjectUtil.equal(item.getMessageType(),"user") ? "客人": "AI" );
                        }
                        record.setContent(item.getContent());
                        return record;
                    }).toList();

                    // 聊天记录转换
                    String chatRecord = records.stream().map(record ->
                         record.getName() + ": " + record.getContent()
                    ).collect(Collectors.joining("\n"));


                    HotelTicketsDetailExportDTO dto = new HotelTicketsDetailExportDTO();

                    // 映射字段
                    dto.setTicketNo(ticket.getTicketNo())
                       .setType(ticket.getTicketCategoryName())
                       .setPositionName(ticket.getPositionName())
                       .setGuestRequest(ticket.getGuestRequest())
                       .setConversationType(ticket.getChatType())
                       .setHandleMethod(ticket.getHandleMethodDesc())
                       .setStatus(ticket.getShowFlag())
                       .setCreator(ticket.getCreatedByName())
                       .setHandler(ticket.getCompletedByName())
                       .setCreateTime(ticket.getCreatedAt())
                       .setHandleTime(ticket.getCompletedAt())
                       .setHandleDuration(CustomDateUtil.calculateDuration(ticket.getCreatedAt(), ticket.getCompletedAt()))
                       .setChatRecord(chatRecord);
                    return Mono.just(dto);
                });
    }








    @Override
    public List<String> convertToRow(HotelTicketsDetailExportDTO data) {
        Map<String, Function<HotelTicketsDetailExportDTO, String>> extractors = new HashMap<>();
        
        extractors.put(FIELD_TICKET_NO, HotelTicketsDetailExportDTO::getTicketNo);
        extractors.put(FIELD_TICKET_TYPE, HotelTicketsDetailExportDTO::getType);
        extractors.put(FIELD_TICKET_POSITION_NAME, HotelTicketsDetailExportDTO::getPositionName);
        extractors.put(FIELD_TICKET_GUEST_REQUEST, HotelTicketsDetailExportDTO::getGuestRequest);
        extractors.put(FIELD_TICKET_CONVERSATION_TYPE, HotelTicketsDetailExportDTO::getConversationType);
        extractors.put(FIELD_TICKET_HANDLE_METHOD, HotelTicketsDetailExportDTO::getHandleMethod);
        extractors.put(FIELD_TICKET_STATUS, HotelTicketsDetailExportDTO::getStatus);
        extractors.put(FIELD_TICKET_CREATOR, HotelTicketsDetailExportDTO::getCreator);
        extractors.put(FIELD_TICKET_HANDLER, HotelTicketsDetailExportDTO::getHandler);
        extractors.put(FIELD_TICKET_CREATE_TIME, HotelTicketsDetailExportDTO::getCreateTime);
        extractors.put(FIELD_TICKET_HANDLE_TIME, HotelTicketsDetailExportDTO::getHandleTime);
        extractors.put(FIELD_TICKET_HANDLE_DURATION, HotelTicketsDetailExportDTO::getHandleDuration);
        extractors.put(FIELD_TICKET_CHAT_RECORD, HotelTicketsDetailExportDTO::getChatRecord);

        // 按照表头顺序提取数据
        List<String> row = new ArrayList<>();
        for (String header : getExcelHeaders()) {
            Function<HotelTicketsDetailExportDTO, String> extractor = extractors.get(header);
            if (extractor != null) {
                String value = extractor.apply(data);
                row.add(value != null ? value : "");
            } else {
                row.add("");
            }
        }

        return row;
    }

    @Override
    public String getExportFileName(HotelTicketsDetailExportReq request) {
        if (StrUtil.isNotBlank(request.getStartTime()) && StrUtil.isNotBlank(request.getEndTime())) {
            return request.getHotelName() + "-" + request.getStartTime() + "~" + request.getEndTime() + "-工单明细数据" + ".xlsx";
        }

        return request.getHotelName() + "-" + request.getFirstCreateTime() + "~" + request.getLastCreateTime() + "-工单明细数据" + ".xlsx";
    }

    @Override
    public Class<HotelTicketsDetailExportReq> getRequestClass() {
        return HotelTicketsDetailExportReq.class;
    }

    @Override
    public Map<String, Integer> getColumnWidthMap() {
        Map<String, Integer> widthMap = new HashMap<>();
        widthMap.put(FIELD_TICKET_NO, 15);
        widthMap.put(FIELD_TICKET_TYPE, 12);
        widthMap.put(FIELD_TICKET_POSITION_NAME, 15);
        widthMap.put(FIELD_TICKET_GUEST_REQUEST, 30);
        widthMap.put(FIELD_TICKET_CONVERSATION_TYPE, 12);
        widthMap.put(FIELD_TICKET_HANDLE_METHOD, 12);
        widthMap.put(FIELD_TICKET_STATUS, 10);
        widthMap.put(FIELD_TICKET_CREATOR, 12);
        widthMap.put(FIELD_TICKET_HANDLER, 12);
        widthMap.put(FIELD_TICKET_CREATE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_TIME, 18);
        widthMap.put(FIELD_TICKET_HANDLE_DURATION, 12);
        widthMap.put(FIELD_TICKET_CHAT_RECORD, 25);
        return widthMap;
    }

    @Override
    public Map<String, String> getColumnFormatMap() {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put(FIELD_TICKET_CREATE_TIME, "date");
        formatMap.put(FIELD_TICKET_HANDLE_TIME, "date");
        return formatMap;
    }
}
