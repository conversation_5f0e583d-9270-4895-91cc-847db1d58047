package com.wormhole.hotelds.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;
import java.time.LocalDateTime;

@Slf4j
public class CustomDateUtil {


    // 解析日期时间字符串
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        try {
            // 假设日期格式为 "yyyy-MM-dd HH:mm:ss"，根据实际格式调整
            return LocalDateTime.parse(dateTimeStr.replace(" ", "T"));
        } catch (Exception e) {
            log.warn("解析日期时间失败: {}", dateTimeStr, e);
            return null;
        }
    }



    // 计算处理时长
    public static String calculateDuration(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return "-";
        }
        try {
            LocalDateTime start = parseDateTime(startTime);
            LocalDateTime end = parseDateTime(endTime);
            if (start != null && end != null) {
                long seconds = java.time.Duration.between(start, end).toSeconds();
                if (seconds > 0) {
                    return new DecimalFormat("#0.0").format((double) seconds / 60) + "分";
                }
            }
        } catch (Exception e) {
            log.warn("计算处理时长失败: startTime={}, endTime={}", startTime, endTime, e);
        }
        return "-";
    }
}
