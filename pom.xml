<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.wormhole</groupId>
        <artifactId>wormhole-parent</artifactId>
        <version>1.3.8</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>wormhole-hotelds</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>wormhole-hotelds</name>
    <packaging>pom</packaging>

    <modules>
        <module>wormhole-hotelds-client</module>
        <module>wormhole-hotelds-service</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <hanlp.version>portable-1.8.4</hanlp.version>
        <aliyun-sdk-oss.version>3.18.1</aliyun-sdk-oss.version>
        <annotations.version>24.1.0</annotations.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <lark.version>2.2.4</lark.version>
        <wormhole.version>1.2.0</wormhole.version>
        <hotelds-api.version>1.2.9-mzy-SNAPSHOT</hotelds-api.version>
        <wormhole-chanel.version>1.1.11</wormhole-chanel.version>
        <zxing.version>3.5.3</zxing.version>
        <yop-java-sdk-biz.version>4.4.12</yop-java-sdk-biz.version>
        <yop-java-sdk.version>4.4.15</yop-java-sdk.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-hotelds-api-client</artifactId>
                <version>${hotelds-api.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-trace</artifactId>
                <version>${wormhole.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-wechat-starter</artifactId>
                <version>${wormhole.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-mq-starter</artifactId>
                <version>${wormhole.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-channel-client</artifactId>
                <version>${wormhole-chanel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>${hanlp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>${annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.yop.sdk</groupId>
                <artifactId>yop-java-sdk-biz</artifactId>
                <!--                <classifier>shade</classifier>-->
                <version>${yop-java-sdk-biz.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yeepay.yop.sdk</groupId>
                <artifactId>yop-java-sdk</artifactId>
                <version>${yop-java-sdk.version}</version>
                <classifier>shade</classifier>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
    </build>

</project>
